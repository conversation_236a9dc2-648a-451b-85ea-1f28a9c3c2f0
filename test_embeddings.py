#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script to check if embeddings are working correctly
"""

from libs.typesense_vector_db import TypesenseVectorDB
import numpy as np

def test_embeddings():
    """Test if embeddings are generated correctly for different texts"""
    print("🧪 TESTING EMBEDDINGS")
    print("="*60)
    
    # Initialize database
    db = TypesenseVectorDB(collection_name='test_documents')
    
    # Test different texts
    test_texts = [
        "xem danh sách xuất hủy thực phẩm",
        "cấu hình nhóm trẻ",
        "thay đổi tiền dịch vụ",
        "làm thế nào để xuất hủy thực phẩm",
        "hello world this is completely different text"
    ]
    
    embeddings = []
    
    print("Generating embeddings for different texts...")
    for i, text in enumerate(test_texts):
        print(f"\n{i+1}. Text: {text}")
        try:
            embedding = db.embeddings.embed_query(text)
            embeddings.append(embedding)
            print(f"   ✅ Generated: {len(embedding)} dimensions")
            print(f"   📊 First 5 values: {embedding[:5]}")
            print(f"   📊 Sum: {sum(embedding):.6f}")
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    # Compare embeddings
    print("\n" + "="*60)
    print("COMPARING EMBEDDINGS")
    print("="*60)
    
    if len(embeddings) >= 2:
        for i in range(len(embeddings)):
            for j in range(i+1, len(embeddings)):
                vec1 = np.array(embeddings[i])
                vec2 = np.array(embeddings[j])
                
                # Cosine similarity
                cosine_sim = np.dot(vec1, vec2) / (np.linalg.norm(vec1) * np.linalg.norm(vec2))
                
                # Check if vectors are identical
                are_identical = np.allclose(vec1, vec2, atol=1e-10)
                
                print(f"\nText {i+1} vs Text {j+1}:")
                print(f"  Cosine similarity: {cosine_sim:.6f}")
                print(f"  Identical vectors: {are_identical}")
                
                if are_identical:
                    print("  ⚠️  WARNING: These embeddings are identical!")
    
    # Test with actual database content
    print("\n" + "="*60)
    print("TESTING DATABASE CONTENT")
    print("="*60)
    
    try:
        # Get a few documents from the database
        search_result = db.client.collections['test_documents'].documents.search({
            'q': '*',
            'per_page': 3
        })
        
        print(f"Found {search_result['found']} documents in database")
        
        for i, hit in enumerate(search_result['hits']):
            doc = hit['document']
            print(f"\nDocument {i+1}:")
            print(f"  Content: {doc['content'][:100]}...")
            
            # Get stored embedding
            stored_embedding = doc.get('embedding', [])
            if stored_embedding:
                print(f"  Stored embedding: {len(stored_embedding)} dimensions")
                print(f"  First 5 values: {stored_embedding[:5]}")
                print(f"  Sum: {sum(stored_embedding):.6f}")
                
                # Generate fresh embedding for the same content
                fresh_embedding = db.embeddings.embed_query(doc['content'])
                print(f"  Fresh embedding: {len(fresh_embedding)} dimensions")
                print(f"  First 5 values: {fresh_embedding[:5]}")
                print(f"  Sum: {sum(fresh_embedding):.6f}")
                
                # Compare
                stored_vec = np.array(stored_embedding)
                fresh_vec = np.array(fresh_embedding)
                cosine_sim = np.dot(stored_vec, fresh_vec) / (np.linalg.norm(stored_vec) * np.linalg.norm(fresh_vec))
                are_identical = np.allclose(stored_vec, fresh_vec, atol=1e-6)
                
                print(f"  Stored vs Fresh similarity: {cosine_sim:.6f}")
                print(f"  Are identical: {are_identical}")
                
                if not are_identical:
                    print("  ⚠️  WARNING: Stored and fresh embeddings differ!")
            else:
                print("  ❌ No embedding found in document")
                
    except Exception as e:
        print(f"❌ Error testing database content: {e}")

if __name__ == "__main__":
    test_embeddings()
