#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Debug script to investigate vector search issues
"""

from libs.typesense_vector_db import TypesenseVectorDB
import json

def debug_vector_search():
    """Debug vector search functionality"""
    print("🔍 DEBUGGING VECTOR SEARCH")
    print("="*60)
    
    # Initialize database
    db = TypesenseVectorDB(collection_name='test_documents')
    
    # Test query
    query = "xem danh sách xuất hủy thực phẩm"
    print(f"Query: {query}")
    
    # 1. Get embedding for the query
    print("\n1. Getting embedding for query...")
    try:
        query_embedding = db.embeddings.embed_query(query)
        print(f"   ✅ Embedding generated: {len(query_embedding)} dimensions")
        print(f"   📊 First 5 values: {query_embedding[:5]}")
    except Exception as e:
        print(f"   ❌ Error generating embedding: {e}")
        return
    
    # 2. Test direct Typesense vector search
    print("\n2. Testing direct Typesense vector search...")
    try:
        search_params = {
            'searches': [{
                'collection': 'test_documents',
                'q': '*',
                'vector_query': f'embedding:([{",".join(map(str, query_embedding))}], k:5)',
                'per_page': 5
            }]
        }
        
        search_results = db.client.multi_search.perform(search_params, {})
        
        if search_results and 'results' in search_results:
            result = search_results['results'][0]
            print(f"   ✅ Search completed: {result.get('found', 0)} results")
            
            for i, hit in enumerate(result.get('hits', [])[:3]):
                doc = hit['document']
                vector_distance = hit.get('vector_distance', 'N/A')
                similarity = 1 - vector_distance if vector_distance != 'N/A' else 'N/A'
                
                print(f"\n   📄 Result {i+1}:")
                print(f"      Vector Distance: {vector_distance}")
                print(f"      Similarity: {similarity}")
                print(f"      Content: {doc['content'][:100]}...")
                
        else:
            print("   ❌ No results returned")
            
    except Exception as e:
        print(f"   ❌ Error in vector search: {e}")
    
    # 3. Test with known good document
    print("\n3. Testing with known good document...")
    try:
        # Get a document we know contains the answer
        text_search = db.client.collections['test_documents'].documents.search({
            'q': 'xuất hủy thực phẩm',
            'query_by': 'content',
            'per_page': 1
        })
        
        if text_search['found'] > 0:
            good_doc = text_search['hits'][0]['document']
            print(f"   📄 Found document: {good_doc['content'][:100]}...")
            
            # Get its embedding and compare
            good_content = good_doc['content']
            good_embedding = db.embeddings.embed_query(good_content)
            
            print(f"   📊 Document embedding: {len(good_embedding)} dimensions")
            print(f"   📊 First 5 values: {good_embedding[:5]}")
            
            # Calculate similarity manually
            import numpy as np
            query_vec = np.array(query_embedding)
            doc_vec = np.array(good_embedding)
            
            # Cosine similarity
            cosine_sim = np.dot(query_vec, doc_vec) / (np.linalg.norm(query_vec) * np.linalg.norm(doc_vec))
            print(f"   🎯 Manual cosine similarity: {cosine_sim:.4f}")
            
        else:
            print("   ❌ Could not find good document")
            
    except Exception as e:
        print(f"   ❌ Error in manual comparison: {e}")
    
    # 4. Check collection schema
    print("\n4. Checking collection schema...")
    try:
        collection_info = db.client.collections['test_documents'].retrieve()
        embedding_field = None
        for field in collection_info['fields']:
            if field['name'] == 'embedding':
                embedding_field = field
                break
        
        if embedding_field:
            print(f"   ✅ Embedding field found:")
            print(f"      Type: {embedding_field['type']}")
            print(f"      Dimensions: {embedding_field.get('num_dim', 'N/A')}")
        else:
            print("   ❌ No embedding field found in schema")
            
    except Exception as e:
        print(f"   ❌ Error checking schema: {e}")

if __name__ == "__main__":
    debug_vector_search()
